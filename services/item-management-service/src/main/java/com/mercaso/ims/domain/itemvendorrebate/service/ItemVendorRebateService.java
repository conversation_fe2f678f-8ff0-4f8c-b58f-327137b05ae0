package com.mercaso.ims.domain.itemvendorrebate.service;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Domain service for ItemVendorRebate business logic
 */

public interface ItemVendorRebateService {

    ItemVendorRebate save(ItemVendorRebate itemVendorRebate);

    ItemVendorRebate findById(UUID id);

    List<ItemVendorRebate> findByVendorItemId (UUID vendorItemId);
}
